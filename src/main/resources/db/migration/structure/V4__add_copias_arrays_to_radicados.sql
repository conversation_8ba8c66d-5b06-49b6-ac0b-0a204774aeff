-- Agregar campos de copias internas y externas a radicados
-- Implementación de arrays PostgreSQL para manejo eficiente de copias

-- Agregar columnas para copias internas (array de strings) y externas (array de UUIDs)
ALTER TABLE radicados 
ADD COLUMN IF NOT EXISTS copias_internas text[],
ADD COLUMN IF NOT EXISTS copias_externas uuid[];

-- Agregar índices para optimizar consultas en arrays
CREATE INDEX IF NOT EXISTS idx_radicados_copias_internas ON radicados USING GIN(copias_internas);
CREATE INDEX IF NOT EXISTS idx_radicados_copias_externas ON radicados USING GIN(copias_externas);

-- Comentarios para documentación
COMMENT ON COLUMN radicados.copias_internas IS 'Array de usernames de usuarios internos que reciben copia del documento';
COMMENT ON COLUMN radicados.copias_externas IS 'Array de UUIDs de usuarios externos que reciben copia del documento';

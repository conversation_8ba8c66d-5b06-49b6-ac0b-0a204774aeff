package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import co.com.gedsys.base.infrastructure.data_access.RadicadoEntity;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring",
        uses = {DataAccessConsecutivoMapper.class,
                DataAccessClasificacionDocumentalMapper.class,
                DataAccessUnidadDocumentalMapper.class,
                DefinicionMetadatosDataAccessMapper.class,
                DocumentoDataAccessMapper.class,
                DataAccessTipoDocumentalMapper.class,
                ExternalUsersGatewayMapper.class,
                SeccionPersistenceMapper.class},
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public abstract class DataAccessRadicadoMapper {

    @Autowired
    protected ExternalUsersRepository externalUsersRepository;

    @Mapping(target = "propiedadesRadicado", source = "entity")
    @Mapping(target = "documento", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
    @Mapping(target = "consecutivo", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
    @Mapping(target = "destinatario", source = "destinatario")
    @Mapping(target = "remitente", source = "remitente")
    @Mapping(target = "destino", source = "destino")
    @Mapping(target = "destinatarioInterno", source = "destinatarioInterno")
    @Mapping(target = "remitenteInterno", source = "remitenteInterno")
    @Mapping(target = "destino.hijos", ignore = true)
    @Mapping(target = "destino.usuarios", ignore = true)
    @Mapping(target = "destino.padre", ignore = true)
    @Mapping(target = "copiasInternas", ignore = true)
    @Mapping(target = "copiasExternas", ignore = true)
    @Mapping(target = "expedir", ignore = true)
    public abstract Radicado toDomain(RadicadoEntity entity);

    @Mapping(target = "propY", source = "propiedadesRadicado.y")
    @Mapping(target = "propX", source = "propiedadesRadicado.x")
    @Mapping(target = "propWidth", source = "propiedadesRadicado.width")
    @Mapping(target = "propPage", source = "propiedadesRadicado.page")
    @Mapping(target = "propHeight", source = "propiedadesRadicado.height")
    @Mapping(target = "propRotationDegrees", source = "propiedadesRadicado.rotationDegrees")
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "documentoId", source = "documento.id")
    @Mapping(target = "consecutivoId", source = "consecutivo.id")
    @Mapping(target = "destinatarioId", source = "destinatario.id")
    @Mapping(target = "destinatario", source = "destinatario")
    @Mapping(target = "remitenteId", source = "remitente.id")
    @Mapping(target = "remitente", source = "remitente")
    @Mapping(target = "destinoId", source = "destino.id")
    @Mapping(target = "destino", source = "destino")
    @Mapping(target = "destinatarioInterno", source = "destinatarioInterno")
    @Mapping(target = "remitenteInterno", source = "remitenteInterno")
    @Mapping(target = "copiasInternas", ignore = true)
    @Mapping(target = "copiasExternas", ignore = true)
    @Mapping(target = "destino.usuarios", ignore = true)
    @Mapping(target = "destino.padre", ignore = true)
    public abstract RadicadoEntity toEntity(Radicado domain);

    @AfterMapping
    protected void mapCopiasAfterToDomain(RadicadoEntity entity, @MappingTarget Radicado radicado) {
        radicado.setCopiasInternas(stringArrayToList(entity.getCopiasInternas()));
        radicado.setCopiasExternas(uuidArrayToExternalUserList(entity.getCopiasExternas()));
    }

    @AfterMapping
    protected void mapCopiasAfterToEntity(Radicado domain, @MappingTarget RadicadoEntity entity) {
        entity.setCopiasInternas(stringListToArray(domain.getCopiasInternas()));
        entity.setCopiasExternas(externalUserListToUuidArray(domain.getCopiasExternas()));
    }

    // Métodos de conversión para arrays
    protected List<String> stringArrayToList(String[] array) {
        return array != null ? Arrays.asList(array) : null;
    }

    protected String[] stringListToArray(List<String> list) {
        return list != null ? list.toArray(new String[0]) : null;
    }

    protected List<ExternalUser> uuidArrayToExternalUserList(UUID[] array) {
        if (array == null) return null;
        return Arrays.stream(array)
                .map(uuid -> externalUsersRepository.findById(uuid).orElse(null))
                .filter(user -> user != null)
                .collect(Collectors.toList());
    }

    protected UUID[] externalUserListToUuidArray(List<ExternalUser> list) {
        if (list == null) return null;
        return list.stream()
                .map(ExternalUser::getId)
                .toArray(UUID[]::new);
    }
}

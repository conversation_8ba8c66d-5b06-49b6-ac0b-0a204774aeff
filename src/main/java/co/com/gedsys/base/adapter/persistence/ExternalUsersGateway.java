package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.adapter.persistence.mappers.ExternalUsersGatewayMapper;
import co.com.gedsys.base.infrastructure.data_access.repository.ExternalUsersJpaRepository;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserIdentificationType;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserStatus;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@RequiredArgsConstructor
@Repository
public class ExternalUsersGateway implements ExternalUsersRepository {
    private final ExternalUsersJpaRepository jpaRepository;
    private final ExternalUsersGatewayMapper mapper;

    @Override
    public ExternalUser save(ExternalUser domainEntity) {
        var entity = mapper.toEntity(domainEntity);
        var saved = jpaRepository.save(entity);
        return mapper.toDomain(saved);
    }

    @Override
    public Optional<ExternalUser> findById(UUID uuid) {
        return jpaRepository.findById(uuid).map(mapper::toDomain);
    }

    @Override
    public List<ExternalUser> findAll() {
        return jpaRepository.findAll().stream().map(mapper::toDomain).toList();
    }

    @Override
    public boolean checkStock(String name) {
        return false;
    }

    @Override
    public boolean checkStock(ExternalUser entity) {
        var matcher = ExampleMatcher.matching()
                .withIgnorePaths("name")
                .withMatcher("identificationType", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("identificationNumber", ExampleMatcher.GenericPropertyMatchers.exact());
        var example = Example.of(mapper.toEntity(entity), matcher);
        return jpaRepository.exists(example);
    }

    @Override
    public void delete(UUID uuid) {
        jpaRepository.findById(uuid)
                .map(e -> e.setStatus(ExternalUserStatus.ELIMINADO)).ifPresent(jpaRepository::save);
    }

    @Override
    public ExternalUser update(UUID id, ExternalUser domainEntity) {
        var entityToUpdate = jpaRepository.findById(id).orElseThrow(() -> new RuntimeException("No se encontró el usuario con id " + id));
        mapper.synchronize(domainEntity, entityToUpdate);

        var propertiesToUpdate = entityToUpdate.getProperties();
        var propertiesForUpdate = domainEntity.getProperties();
        if (propertiesForUpdate != null) {
           for(var propertyEntity : propertiesToUpdate) {
               var propertyToUpdate = propertiesForUpdate.stream()
                   .filter(p -> p.getId() != null && propertyEntity.getId() != null && p.getId().equals(propertyEntity.getId()))
                   .findFirst().orElse(null);
               if (propertyToUpdate != null) {
                   mapper.toEntity(propertyToUpdate, propertyEntity);
               }
           }
        }
        return mapper.toDomain(jpaRepository.save(entityToUpdate));
    }

    @Override
    public boolean existsByNameAndIdentificationType(String name, ExternalUserIdentificationType type) {
        return jpaRepository.existsByNameAndIdentificationType(name, type);
    }

    @Override
    public Optional<ExternalUser> findByNameAndIdentificationType(String name, ExternalUserIdentificationType type) {
        return jpaRepository.findByNameAndIdentificationType(name, type)
                .map(mapper::toDomain);
    }

    @Override
    public List<ExternalUser> findAllByIds(List<UUID> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }
        return jpaRepository.findAllById(ids).stream()
                .map(mapper::toDomain)
                .toList();
    }
}

package co.com.gedsys.base.application.usecase.gestion_tramite.envio_documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import co.com.gedsys.base.domain.organizacion.UsuarioSeccion;
import co.com.gedsys.base.domain.organizacion.TipoRelacionUsuarioSeccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class RadicarDocumentoEnvioDocumentalUseCaseTest {

    @Mock private DocumentoRepository documentoRepository;
    @Mock private ConsecutivoRepository consecutivoRepository;
    @Mock private RadicadoRepository radicadoRepository;
    @Mock private ExternalUsersRepository externalUsersRepository;
    @Mock private DefinicionMetadatosRepository definicionMetadatosRepository;
    @Mock private SeccionRepository seccionRepository;
    @Mock private RadicadoApplicationLayerMapper radicadoMapper;
    @InjectMocks private RadicarDocumentoEnvioDocumentalUseCase useCase;

    private UUID documentoId;
    private UUID destinatarioId;
    private Documento documento;
    private ExternalUser destinatario;
    private UsuarioSeccion usuarioSeccion;
    private co.com.gedsys.base.domain.organizacion.Seccion seccion;

    @BeforeEach
    void setUp() {
        documentoId = UUID.randomUUID();
        destinatarioId = UUID.randomUUID();
        
        // Configuración común de mocks
        documento = mock(Documento.class);
        when(documento.getRadicados()).thenReturn(new ArrayList<>());
        
        Consecutivo consecutivo = mock(Consecutivo.class);
        when(consecutivo.getTipoConsecutivo()).thenReturn(co.com.gedsys.base.domain.consecutivo.TipoConsecutivo.ENVIO);
        when(consecutivoRepository.findByExample(any())).thenReturn(Optional.of(consecutivo));
        
        destinatario = mock(ExternalUser.class);
        when(destinatario.getStatus()).thenReturn(co.com.gedsys.base.domain.usuario_externo.ExternalUserStatus.ACTIVO);
        
        usuarioSeccion = mock(UsuarioSeccion.class);
        when(usuarioSeccion.getUsername()).thenReturn("usuario.test");
        when(usuarioSeccion.getRelacion()).thenReturn(TipoRelacionUsuarioSeccion.PRIMARIA);
        
        seccion = mock(co.com.gedsys.base.domain.organizacion.Seccion.class);
        when(seccion.getUsuarios()).thenReturn(Set.of(usuarioSeccion));
        
        // Configuración común de comportamientos
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documento));
        when(externalUsersRepository.findById(destinatarioId)).thenReturn(Optional.of(destinatario));
        when(seccionRepository.buscarSeccionesPorUsuario("usuario.test")).thenReturn(List.of(seccion));
        when(definicionMetadatosRepository.buscarPorPatrones(any())).thenReturn(Set.of());
        when(radicadoRepository.save(any())).thenReturn(mock(Radicado.class));
        when(radicadoMapper.toDTO(any())).thenReturn(mock(RadicadoDTO.class));
    }

    @Test
    @DisplayName("Debe generar un nuevo radicado de envío correctamente")
    void deberiaCrearUnRadicado() {
        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(
            documentoId, destinatarioId, "usuario.test", List.of(), null, null);
        
        assertDoesNotThrow(() -> useCase.execute(command));
    }

    @Test
    @DisplayName("Debe lanzar excepción si el documento no existe")
    void deberiaLanzarExcepcionSiDocumentoNoExiste() {
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.empty());
        
        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(
            documentoId, destinatarioId, "usuario.test", List.of(), null, null);
            
        assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
    }

    @Test
    @DisplayName("Debe lanzar excepción si el destinatario no existe")
    void deberiaLanzarExcepcionSiDestinatarioNoExiste() {
        when(externalUsersRepository.findById(destinatarioId)).thenReturn(Optional.empty());
        
        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(
            documentoId, destinatarioId, "usuario.test", List.of(), null, null);
            
        assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
    }

    @Test
    @DisplayName("Debe validar que los ExternalUsers de copiasExternas existen")
    void deberiaValidarQueExternalUsersDeCopiasExternasExisten() {
        UUID copiaExternaId1 = UUID.randomUUID();
        UUID copiaExternaId2 = UUID.randomUUID();
        List<UUID> copiasExternas = List.of(copiaExternaId1, copiaExternaId2);

        when(externalUsersRepository.findById(copiaExternaId1)).thenReturn(Optional.of(mock(ExternalUser.class)));
        when(externalUsersRepository.findById(copiaExternaId2)).thenReturn(Optional.empty());

        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(
            documentoId, destinatarioId, "usuario.test", List.of(), null, copiasExternas);

        assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
    }

    @Test
    @DisplayName("Debe permitir radicado cuando todas las copias externas existen")
    void deberiaPermitirRadicadoCuandoTodasLasCopiasExternasExisten() {
        UUID copiaExternaId1 = UUID.randomUUID();
        UUID copiaExternaId2 = UUID.randomUUID();
        List<UUID> copiasExternas = List.of(copiaExternaId1, copiaExternaId2);

        when(externalUsersRepository.findById(copiaExternaId1)).thenReturn(Optional.of(mock(ExternalUser.class)));
        when(externalUsersRepository.findById(copiaExternaId2)).thenReturn(Optional.of(mock(ExternalUser.class)));

        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(
            documentoId, destinatarioId, "usuario.test", List.of(), null, copiasExternas);

        assertDoesNotThrow(() -> useCase.execute(command));
    }
}

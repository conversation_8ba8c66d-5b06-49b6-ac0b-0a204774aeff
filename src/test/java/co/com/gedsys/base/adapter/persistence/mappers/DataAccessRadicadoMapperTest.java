package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import co.com.gedsys.base.infrastructure.data_access.RadicadoEntity;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@SpringBootTest
class DataAccessRadicadoMapperTest {

    @Autowired
    private DataAccessRadicadoMapper mapper;

    @MockBean
    private ExternalUsersRepository externalUsersRepository;

    @Test
    void debeMapearCopiasInternasCorrectamente() {
        // Arrange
        Consecutivo consecutivo = new Consecutivo("PRE", "SUF", 1, TipoConsecutivo.ENVIO);
        Radicado radicado = new Radicado(consecutivo);
        
        List<String> copiasInternas = List.of("usuario1", "usuario2", "usuario3");
        radicado.setCopiasInternas(copiasInternas);

        // Act
        RadicadoEntity entity = mapper.toEntity(radicado);
        Radicado radicadoMapeado = mapper.toDomain(entity);

        // Assert
        assertThat(entity.getCopiasInternas()).containsExactly("usuario1", "usuario2", "usuario3");
        assertThat(radicadoMapeado.getCopiasInternas()).containsExactlyInAnyOrder("usuario1", "usuario2", "usuario3");
    }

    @Test
    void debeMapearCopiasExternasCorrectamente() {
        // Arrange
        Consecutivo consecutivo = new Consecutivo("PRE", "SUF", 1, TipoConsecutivo.ENVIO);
        Radicado radicado = new Radicado(consecutivo);
        
        UUID uuid1 = UUID.randomUUID();
        UUID uuid2 = UUID.randomUUID();
        
        ExternalUser user1 = new ExternalUser("Usuario 1", "CC", "123");
        user1.setId(uuid1);
        ExternalUser user2 = new ExternalUser("Usuario 2", "CC", "456");
        user2.setId(uuid2);
        
        List<ExternalUser> copiasExternas = List.of(user1, user2);
        radicado.setCopiasExternas(copiasExternas);

        // Mock repository responses
        when(externalUsersRepository.findById(uuid1)).thenReturn(Optional.of(user1));
        when(externalUsersRepository.findById(uuid2)).thenReturn(Optional.of(user2));

        // Act
        RadicadoEntity entity = mapper.toEntity(radicado);
        Radicado radicadoMapeado = mapper.toDomain(entity);

        // Assert
        assertThat(entity.getCopiasExternas()).containsExactly(uuid1, uuid2);
        assertThat(radicadoMapeado.getCopiasExternas()).hasSize(2);
        assertThat(radicadoMapeado.getCopiasExternas().get(0).getId()).isEqualTo(uuid1);
        assertThat(radicadoMapeado.getCopiasExternas().get(1).getId()).isEqualTo(uuid2);
        assertThat(radicadoMapeado.getCopiasExternas().get(0).getName()).isEqualTo("Usuario 1");
        assertThat(radicadoMapeado.getCopiasExternas().get(1).getName()).isEqualTo("Usuario 2");
    }

    @Test
    void debeMapearCopiasNulasCorrectamente() {
        // Arrange
        Consecutivo consecutivo = new Consecutivo("PRE", "SUF", 1, TipoConsecutivo.ENVIO);
        Radicado radicado = new Radicado(consecutivo);
        // No se setean copias (quedan null)

        // Act
        RadicadoEntity entity = mapper.toEntity(radicado);
        Radicado radicadoMapeado = mapper.toDomain(entity);

        // Assert
        assertThat(entity.getCopiasInternas()).isNull();
        assertThat(entity.getCopiasExternas()).isNull();
        assertThat(radicadoMapeado.getCopiasInternas()).isNull();
        assertThat(radicadoMapeado.getCopiasExternas()).isNull();
    }

    @Test
    void debeIgnorarUsuariosExternosInexistentes() {
        // Arrange
        UUID uuidExistente = UUID.randomUUID();
        UUID uuidInexistente = UUID.randomUUID();
        
        ExternalUser userExistente = new ExternalUser("Usuario Existente", "CC", "123");
        userExistente.setId(uuidExistente);
        
        // Mock repository responses
        when(externalUsersRepository.findById(uuidExistente)).thenReturn(Optional.of(userExistente));
        when(externalUsersRepository.findById(uuidInexistente)).thenReturn(Optional.empty());

        // Crear entity con UUIDs (uno existente, uno inexistente)
        RadicadoEntity entity = new RadicadoEntity();
        entity.setCopiasExternas(new UUID[]{uuidExistente, uuidInexistente});

        // Act
        Radicado radicado = mapper.toDomain(entity);

        // Assert - Solo debe incluir el usuario existente
        assertThat(radicado.getCopiasExternas()).hasSize(1);
        assertThat(radicado.getCopiasExternas().get(0).getId()).isEqualTo(uuidExistente);
        assertThat(radicado.getCopiasExternas().get(0).getName()).isEqualTo("Usuario Existente");
    }
}

---
type: "always_apply"
---

- Implementaciones quirúrgicas y minimalistas que alcanzan el objetivo con la mínima intervención posible.
- Procura dividir el trabajo en tareas relacionadas y cuidadosamente planificadas.
- Asegúrate de seguir la Clean Architecture del proyecto siempre que la tarea involucre diferentes capas.
- Naming convention: Use spanish for tests  and domain and business logic. Use english for generic names or private or internal variables.